import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { SelectModule } from 'primeng/select';
import { DatePickerModule } from 'primeng/datepicker';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FileUploadModule } from 'primeng/fileupload';
import { TabViewModule } from 'primeng/tabview';
import { MultiSelectModule } from 'primeng/multiselect';

import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { ChipModule } from 'primeng/chip';
import { MessageService } from 'primeng/api';
import { ErrorHandlingService } from '../shared/services/error-handling.service';
import {
    EvrakDetay,
    Hedef,
    IDHedefDetay,
    MahkemeKararDetay,
    MahkemeKararBilgisi,
    MakosControllerService,
    IDYeniKararRequestDetay,
    IDUzatmaKarariRequestDetay,
    IDSonlandirmaKarariRequestDetay,
    IDHedefGuncellemeRequestDetay,
    IDMahkemeKararGuncellemeRequestDetay,
    IDAidiyatBilgisiGuncellemeRequestDetay,
    IDSucTipiGuncellemeRequestDetay
} from '../../generated-api';
import { KararTuruEnum, KararTuruOptions } from '../shared/enums/KararTuruEnum';
import { formatEnumLabel } from '../shared/utils/enumutil';
import { MahkemeKararPreviewComponent } from '../shared/components/mahkeme-karar-preview/mahkeme-karar-preview.component';
import { JsonImportExportComponent } from '../shared/components/json-import-export/json-import-export.component';
import { MahkemeKararDetaylariComponent } from '../shared/components/mahkeme-karar-detaylari/mahkeme-karar-detaylari.component';
import HedefTipEnum = Hedef.HedefTipEnum;
import SureTipEnum = IDHedefDetay.SureTipEnum;
import EvrakTuruEnum = EvrakDetay.EvrakTuruEnum;
import MahkemeKararTipiEnum = MahkemeKararBilgisi.MahkemeKararTipiEnum;

@Component({
    selector: 'app-mahkeme-karar-talep',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CardModule,
        ButtonModule,
        InputTextModule,
        SelectModule,
        DatePickerModule,
        CheckboxModule,
        ToastModule,
        ProgressSpinnerModule,
        FileUploadModule,
        TabViewModule,
        MultiSelectModule,
        TableModule,
        DialogModule,
        ChipModule,
        MahkemeKararPreviewComponent,
        JsonImportExportComponent,
        MahkemeKararDetaylariComponent
    ],
    providers: [],
    templateUrl: './mahkeme-karar-talep.component.html',
    styleUrls: ['./mahkeme-karar-talep.component.scss']
})
export class MahkemeKararTalepComponent implements OnInit {
    commonForm!: FormGroup;
    idYeniKararForm!: FormGroup;
    idUzatmaKarariForm!: FormGroup;
    idSonlandirmaKarariForm!: FormGroup;
    idHedefGuncellemeForm!: FormGroup;
    idMahkemeKararGuncellemeForm!: FormGroup;
    idAidiyatGuncellemeForm!: FormGroup;
    idSucTipiGuncellemeForm!: FormGroup;
    itKararForm!: FormGroup;

    // Enum'u template'te kullanabilmek için public olarak tanımlıyoruz (ENUM'u HTML'e açmak için:)
    KararTuruEnum = KararTuruEnum;
    kararTuru: KararTuruEnum | null = null;

    onKararTuruChanged(event: any): void {
        // this.kararTuru = KararTuruEnum[event.value as keyof typeof KararTuruEnum] // enum olarak saklıyoruz
    }

    loading = false;
    seciliDosya: File | null = null;
    activeTabIndex = 0;

    // Hedef dialog properties
    hedefDialogVisible = false;
    hedefForm!: FormGroup;
    editingHedefIndex = -1;

    // Dropdown options and loading states
    iller: any[] = [];
    kurumlar: any[] = [];
    mahkemeKodlari: any[] = [];
    sucTipleri: any[] = [];

    dropdownLoading = {
        iller: false,
        kurumlar: false,
        mahkemeKodlari: false,
        sucTipleri: false,
        mahkemeKararTipleri: false,
        tespitTurleri: false
    };

    // Form arrays for hedef detayları
    get hedefDetayListesi(): FormArray {
        return this.idYeniKararForm.get('hedefDetayListesi') as FormArray;
    }

    get hedefler(): any[] {
        return this.hedefDetayListesi?.value || [];
    }

    // Aidiyat kodları için
    newAidiyatKodu: string = '';
    aidiyatKodlariOptions: any[] = [];

    // Suç tipi kodları için
    newSucTipiKodu: string = '';

    // Hedef aidiyat kodları için
    newHedefAidiyatKodu: string = '';
    hedefAidiyatKodlariOptions: any[] = [];

    // Hedef güncelleme karar detayları için
    hedefGuncellemeKararDetayListesi: any[] = [];

    // Mahkeme karar güncelleme detayları için
    mahkemeKararGuncellemeDetayListesi: any[] = [];

    // Aidiyat güncelleme karar detayları için
    aidiyatGuncellemeKararDetayListesi: any[] = [];

    // Suç tipi güncelleme karar detayları için
    sucTipiGuncellemeKararDetayListesi: any[] = [];

    // IT hedef detayları için
    itHedefDetayListesi: any[] = [];

    // Preview için
    previewVisible: boolean = false;

    // JSON Import/Export için
    jsonImportExportVisible: boolean = false;

    kararTuruOptions: { label: string; value: string }[] = [];

    // Enum değerlerinden dinamik olarak oluşturulan seçenek listeleri
    evrakTuruOptions: { label: string; value: string }[] = [];
    mahkemeKararTipOptions: { label: string; value: string }[] = [];
    hedefTipOptions: { label: string; value: string }[] = [];
    sureTipiOptions: { label: string; value: string }[] = [];

    constructor(
        private fb: FormBuilder,
        private makosService: MakosControllerService,
        private messageService: MessageService,
        private errorHandlingService: ErrorHandlingService
    ) {}

    ngOnInit(): void {
        // Enum değerlerinden dinamik olarak seçenek listelerini oluştur
        this.initializeEnumOptions();

        // Formları oluştur
        this.createForms();

        // API'den gelen verileri yükle
        this.loadDropdownData();
    }

    private initializeEnumOptions(): void {
        // KararTuruEnum değerlerinden seçenek listesi oluştur
        this.kararTuruOptions = KararTuruOptions;

        // EvrakTuruEnum değerlerinden seçenek listesi oluştur
        this.evrakTuruOptions = Object.entries(EvrakTuruEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // HedefTipEnum değerlerinden seçenek listesi oluştur
        this.hedefTipOptions = Object.entries(HedefTipEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // SureTipEnum değerlerinden seçenek listesi oluştur
        this.sureTipiOptions = Object.entries(SureTipEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // MahkemeKararTipiEnum değerlerinden seçenek listesi oluştur
        this.mahkemeKararTipOptions = Object.entries(MahkemeKararTipiEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));
    }

    private loadDropdownData(): void {
        // Load dropdown data for ID Yeni Karar
        this.loadIller();
        this.loadKurumlar();
        this.loadMahkemeKodlari();
        this.loadSucTipleri();
        // this.loadMahkemeKararTipleri(); // Artık enum değerlerinden oluşturulduğu için API çağrısına gerek yok
        // this.loadTespitTurleri();
    }

    private loadIller(): void {
        this.dropdownLoading.iller = true;
        this.makosService.iller().subscribe({
            next: (response) => {
                if (response.success && response.result?.iller) {
                    this.iller = response.result.iller.map((item) => ({
                        label: `${item.ilAdi || ''} ${item.ilceAdi || ''}`.trim(),
                        value: item.ilKod || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'İller yüklenemedi.'
                    });
                }
                this.dropdownLoading.iller = false;
            },
            error: (error) => {
                console.error('İller yükleme hatası:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Hata',
                    detail: 'İller yüklenirken bir hata oluştu.'
                });
                this.dropdownLoading.iller = false;
            }
        });
    }

    private loadKurumlar(): void {
        this.dropdownLoading.kurumlar = true;
        this.makosService.kurumlar().subscribe({
            next: (response) => {
                if (response.success && response.result?.kurumlar) {
                    this.kurumlar = response.result.kurumlar.map((item) => ({
                        label: item.kurumAdi || '',
                        value: item.kurumKod || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Kurumlar yüklenemedi.'
                    });
                }
                this.dropdownLoading.kurumlar = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Kurumlar', () => {
                    this.dropdownLoading.kurumlar = false;
                });
            }
        });
    }

    private loadMahkemeKodlari(): void {
        this.dropdownLoading.mahkemeKodlari = true;
        this.makosService.mahkemeKodlari().subscribe({
            next: (response) => {
                if (response.success && response.result?.mahkemeKodListesi) {
                    this.mahkemeKodlari = response.result.mahkemeKodListesi.map((item) => ({
                        label: item.mahkemeAdi || '',
                        value: item.mahkemeKodu || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Mahkeme kodları yüklenemedi.'
                    });
                }
                this.dropdownLoading.mahkemeKodlari = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Mahkeme kodları', () => {
                    this.dropdownLoading.mahkemeKodlari = false;
                });
            }
        });
    }

    private loadSucTipleri(): void {
        this.dropdownLoading.sucTipleri = true;
        this.makosService.sucTipleri().subscribe({
            next: (response) => {
                if (response.success && response.result?.sucTipleri) {
                    this.sucTipleri = response.result.sucTipleri.map((item) => ({
                        label: item.aciklama || '',
                        value: item.sucTipiKodu || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Suç tipleri yüklenemedi.'
                    });
                }
                this.dropdownLoading.sucTipleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Suç tipleri', () => {
                    this.dropdownLoading.sucTipleri = false;
                });
            }
        });
    }

    // private loadMahkemeKararTipleri(): void {
    //     this.dropdownLoading.mahkemeKararTipleri = true;
    //
    //     // Enum değerlerinden dinamik olarak seçenek listesi oluştur
    //     this.mahkemeKararTipOptions = Object.entries(MahkemeKararTipiEnum).map(([key, value]) => ({
    //         label: formatEnumLabel(key),
    //         value: value
    //     }));
    //
    //     this.dropdownLoading.mahkemeKararTipleri = false;
    // }

    // private loadTespitTurleri(): void {
    //     this.dropdownLoading.tespitTurleri = true;
    //     this.makosService.tespitTurleri().subscribe({
    //         next: (response) => {
    //             if (response.success && response.result?.tespitTurleri) {
    //                 this.hedefTipOptions = response.result.tespitTurleri.map(item => ({
    //                     label: item.aciklama || '',
    //                     value: item.tespitTuru?.toString() || ''
    //                 }));
    //             } else {
    //                 this.messageService.add({
    //                     severity: 'error',
    //                     summary: 'Hata',
    //                     detail: 'Tespit türleri yüklenemedi.'
    //                 });
    //             }
    //             this.dropdownLoading.tespitTurleri = false;
    //         },
    //         error: (error) => {
    //             this.errorHandlingService.handleDropdownError('Tespit türleri', () => {
    //                 this.dropdownLoading.tespitTurleri = false;
    //             });
    //         }
    //     });
    // }

    addNewAidiyatKodu(): void {
        if (this.newAidiyatKodu && this.newAidiyatKodu.trim()) {
            const trimmedKod = this.newAidiyatKodu.trim();

            // Check if the code already exists in options
            const exists = this.aidiyatKodlariOptions.some((option) => option.value === trimmedKod);

            if (!exists) {
                // Create new array with new option to ensure proper change detection
                this.aidiyatKodlariOptions = [
                    ...this.aidiyatKodlariOptions,
                    {
                        label: trimmedKod,
                        value: trimmedKod
                    }
                ];

                // Add to form control value - ensure we're working with a new array
                const currentValues = [...(this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.value || [])];
                if (!currentValues.includes(trimmedKod)) {
                    currentValues.push(trimmedKod);
                    this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.setValue(currentValues);
                }

                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Aidiyat kodu "${trimmedKod}" eklendi.`
                });
            } else {
                this.messageService.add({
                    severity: 'warn',
                    summary: 'Uyarı',
                    detail: 'Bu aidiyat kodu zaten mevcut.'
                });
            }

            // Clear input
            this.newAidiyatKodu = '';
        }
    }

    addNewSucTipiKodu(): void {
        if (this.newSucTipiKodu && this.newSucTipiKodu.trim()) {
            const trimmedKod = this.newSucTipiKodu.trim();

            // Check if the code already exists in options
            const exists = this.sucTipleri.some((option) => option.value === trimmedKod);

            if (!exists) {
                // Create new array with new option to ensure proper change detection
                this.sucTipleri = [
                    ...this.sucTipleri,
                    {
                        label: trimmedKod,
                        value: trimmedKod
                    }
                ];

                // Add to form control value - ensure we're working with a new array
                const currentValues = [...(this.idYeniKararForm.get('mahkemeSucTipiKodlari')?.value || [])];
                if (!currentValues.includes(trimmedKod)) {
                    currentValues.push(trimmedKod);
                    this.idYeniKararForm.get('mahkemeSucTipiKodlari')?.setValue(currentValues);
                }

                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Suç tipi kodu "${trimmedKod}" eklendi.`
                });
            } else {
                this.messageService.add({
                    severity: 'warn',
                    summary: 'Uyarı',
                    detail: 'Bu suç tipi kodu zaten mevcut.'
                });
            }

            // Clear input
            this.newSucTipiKodu = '';
        }
    }

    addNewHedefAidiyatKodu(): void {
        if (this.newHedefAidiyatKodu && this.newHedefAidiyatKodu.trim()) {
            const trimmedKod = this.newHedefAidiyatKodu.trim();

            // Check if the code already exists in options
            const exists = this.hedefAidiyatKodlariOptions.some((option) => option.value === trimmedKod);

            if (!exists) {
                // Create new array with new option to ensure proper change detection
                this.hedefAidiyatKodlariOptions = [
                    ...this.hedefAidiyatKodlariOptions,
                    {
                        label: trimmedKod,
                        value: trimmedKod
                    }
                ];

                // Add to form control value - ensure we're working with a new array
                const currentValues = [...(this.hedefForm.get('hedefAidiyatKodlari')?.value || [])];
                if (!currentValues.includes(trimmedKod)) {
                    currentValues.push(trimmedKod);
                    this.hedefForm.get('hedefAidiyatKodlari')?.setValue(currentValues);
                }

                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Hedef aidiyat kodu "${trimmedKod}" eklendi.`
                });
            } else {
                this.messageService.add({
                    severity: 'warn',
                    summary: 'Uyarı',
                    detail: 'Bu hedef aidiyat kodu zaten mevcut.'
                });
            }

            // Clear input
            this.newHedefAidiyatKodu = '';
        }
    }

    private createForms(): void {
        this.commonForm = this.fb.group({
            kararTuru: [this.kararTuru, Validators.required],
            evrakNo: ['', Validators.required],
            evrakTarihi: [null, Validators.required],
            evrakKurumKodu: ['', Validators.required],
            evrakTuru: ['', Validators.required],
            geldigiIlIlceKodu: ['', Validators.required],
            havaleBirimi: ['', [Validators.maxLength(10)]],
            evrakKonusu: [''],
            evrakAciklama: [''],
            acilmi: [false],
            mahkemeKararTipi: ['', Validators.required],
            mahkemeKararDetaylari: [null, Validators.required]
        });

        this.idYeniKararForm = this.fb.group({
            hedefDetayListesi: this.fb.array([]),
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.idUzatmaKarariForm = this.fb.group({
            hedefDetayListesi: this.fb.array([]),
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.idSonlandirmaKarariForm = this.fb.group({
            hedefDetayListesi: this.fb.array([]),
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.idHedefGuncellemeForm = this.fb.group({
            hedefGuncellemeKararDetayListesi: this.fb.array([])
        });

        this.idMahkemeKararGuncellemeForm = this.fb.group({
            mahkemeKararGuncellemeDetayListesi: this.fb.array([])
        });

        this.idAidiyatGuncellemeForm = this.fb.group({
            aidiyatGuncellemeKararDetayListesi: this.fb.array([])
        });

        this.idSucTipiGuncellemeForm = this.fb.group({
            sucTipiGuncellemeKararDetayListesi: this.fb.array([])
        });

        this.itKararForm = this.fb.group({
            hedefDetayListesi: this.fb.array([])
        });

        // Hedef form for dialog
        this.hedefForm = this.fb.group({
            hedefNo: ['', Validators.required],
            hedefTip: [HedefTipEnum.Gsm, Validators.required],
            hedefAd: ['', Validators.required],
            hedefSoyad: ['', Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: ['30'],
            sureTip: [SureTipEnum.Gun],
            hedefAidiyatKodlari: [[], Validators.required],
            canakNo: [''],
            ilgiliMahkemeKararDetayi: [null],
            uzatmaSayisi: [null],
            tcKimlikNo: [''],
            acilmi: [false]
        });

        this.commonForm.get('kararTuru')?.valueChanges.subscribe((value) => {
            this.kararTuru = value;
            this.activeTabIndex = 0;
        });
    }

    /**
     * Form verilerinden request nesnesini hazırlar
     * @returns Request nesnesi (IDYeniKararRequest, ITKararRequest veya IDMahkemeKararGuncellemeRequest için temel nesne)
     */
    private prepareRequestData(): any {
        const commonData = this.commonForm.value;

        return {
            id: crypto.randomUUID(), // Benzersiz ID ekliyoruz
            kararTuru: commonData.kararTuru, // Enum değerleri doğrudan backend ile uyumlu
            evrakDetay: {
                evrakNo: commonData.evrakNo,
                evrakTarihi: commonData.evrakTarihi,
                evrakKurumKodu: commonData.evrakKurumKodu,
                evrakTuru: commonData.evrakTuru,
                geldigiIlIlceKodu: commonData.geldigiIlIlceKodu,
                havaleBirimi: commonData.havaleBirimi,
                evrakKonusu: commonData.evrakKonusu,
                evrakAciklama: commonData.evrakAciklama,
                acilmi: commonData.acilmi
            },
            mahkemeKararBilgisi: {
                mahkemeKararTipi: commonData.mahkemeKararTipi,
                mahkemeKararDetay: commonData.mahkemeKararDetaylari
            }
        };
    }

    async onSubmit(): Promise<void> {
        if (this.commonForm.invalid) {
            Object.keys(this.commonForm.controls).forEach((key) => {
                this.commonForm.get(key)?.markAsTouched();
            });
            this.messageService.add({
                severity: 'error',
                summary: 'Hata',
                detail: 'Lütfen tüm zorunlu alanları doldurun.'
            });
            return;
        }

        this.loading = true;
        const file = this.seciliDosya || new Blob();

        // Karar türüne göre özel alanları içeren tam request nesnesini hazırla
        const requestData = this.prepareFullRequestData();

        switch (requestData.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                // ID_YENI_KARAR için doğrudan JSON nesnesi gönderiyoruz
                this.makosService.yeniKararID(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminTespiti:
                // IT_KARAR için doğrudan JSON nesnesi gönderiyoruz
                this.makosService.yeniKararIT(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                // ID Uzatma Kararı için uzatmaKarariID endpoint'ini çağır
                this.makosService.uzatmaKarariID(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                // ID Sonlandırma Kararı için sonlandirmaKarariID endpoint'ini çağır
                this.makosService.sonlandirmaKarariID(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                // ID Hedef Güncelleme için hedefBilgisiGuncelle endpoint'ini çağır
                this.makosService.hedefBilgisiGuncelle(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                // ID Mahkeme Karar Güncelleme için mahkemeBilgisiGuncelle endpoint'ini çağır
                this.makosService.mahkemeBilgisiGuncelle(requestData, file).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                // ID Aidiyat Güncelleme için aidiyatBilgisiGuncelle endpoint'ini çağır
                this.makosService.aidiyatBilgisiGuncelle(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                // ID Suç Tipi Güncelleme için sucTipiGuncelle endpoint'ini çağır
                this.makosService.sucTipiGuncelle(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
        }
    }

    /**
     * Form verilerini hazırlar ve FormData nesnesini döndürür
     * @returns FormData nesnesi
     */
    private prepareFormData(): FormData {
        const formData = new FormData();
        const request = this.prepareFullRequestData();

        formData.append('mahkemeKararDetay', new Blob([JSON.stringify(request)], { type: 'application/json' }));

        if (this.seciliDosya) {
            formData.append('mahkemeKararDosyasi', this.seciliDosya);
        }

        return formData;
    }

    /**
     * Karar türüne göre özel alanları içeren tam request nesnesini hazırlar
     * @returns Tam request nesnesi
     */
    private prepareFullRequestData(): any {
        // Temel request nesnesini oluştur
        const request = this.prepareRequestData();

        // Karar türüne göre özel alanları requestDetay property'si altında topla
        switch (request.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                // ID Yeni Karar için yeniKararRequestDetay property'si altında topla
                request.yeniKararRequestDetay = {
                    hedefDetayListesi: this.hedefDetayListesi.value.map((hedef: any) => this.transformHedefToBackendFormat(hedef)) || [],
                    mahkemeAidiyatKodlari: this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.value || [],
                    mahkemeSucTipiKodlari: this.idYeniKararForm.get('mahkemeSucTipiKodlari')?.value || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                // ID Uzatma Kararı için uzatmaKarariRequestDetay property'si altında topla
                request.uzatmaKarariRequestDetay = {
                    hedefDetayListesi: this.hedefDetayListesi.value.map((hedef: any) => this.transformHedefToBackendFormat(hedef)) || [],
                    mahkemeAidiyatKodlari: this.idUzatmaKarariForm.get('mahkemeAidiyatKodlari')?.value || [],
                    mahkemeSucTipiKodlari: this.idUzatmaKarariForm.get('mahkemeSucTipiKodlari')?.value || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                // ID Sonlandırma Kararı için sonlandirmaKarariRequestDetay property'si altında topla
                request.sonlandirmaKarariRequestDetay = {
                    hedefDetayListesi: this.hedefDetayListesi.value.map((hedef: any) => this.transformHedefToBackendFormat(hedef)) || [],
                    mahkemeAidiyatKodlari: this.idSonlandirmaKarariForm.get('mahkemeAidiyatKodlari')?.value || [],
                    mahkemeSucTipiKodlari: this.idSonlandirmaKarariForm.get('mahkemeSucTipiKodlari')?.value || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                // ID Hedef Güncelleme için hedefGuncellemeRequestDetay property'si altında topla
                request.hedefGuncellemeRequestDetay = {
                    hedefGuncellemeKararDetayListesi: this.hedefGuncellemeKararDetayListesi || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                // ID Mahkeme Karar Güncelleme için mahkemeKararGuncellemeRequestDetay property'si altında topla
                request.mahkemeKararGuncellemeRequestDetay = {
                    mahkemeKararGuncellemeDetayListesi: this.mahkemeKararGuncellemeDetayListesi || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                // ID Aidiyat Güncelleme için idAidiyatBilgisiGuncellemeRequestDetay property'si altında topla
                request.idAidiyatBilgisiGuncellemeRequestDetay = {
                    aidiyatGuncellemeKararDetayListesi: this.aidiyatGuncellemeKararDetayListesi || []
                };
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                // ID Suç Tipi Güncelleme için sucTipiGuncellemeRequestDetay property'si altında topla
                request.sucTipiGuncellemeRequestDetay = {
                    sucTipiGuncellemeKararDetayListesi: this.sucTipiGuncellemeKararDetayListesi || []
                };
                break;
            case KararTuruEnum.IletisiminTespiti:
                // İletişimin Tespiti için IT hedef detaylarını ekle (bu değişmedi)
                request.hedefDetayListesi = this.itHedefDetayListesi || [];
                break;
        }

        return request;
    }

    private transformHedefToBackendFormat(hedef: any): any {
        return {
            hedefNoAdSoyad: {
                hedef: {
                    hedefNo: hedef.hedefNo,
                    hedefTip: hedef.hedefTip
                },
                hedefAd: hedef.hedefAd,
                hedefSoyad: hedef.hedefSoyad
            },
            baslamaTarihi: hedef.baslamaTarihi,
            sure: hedef.sure,
            sureTip: hedef.sureTip,
            hedefAidiyatKodlari: hedef.hedefAidiyatKodlari,
            canakNo: hedef.canakNo,
            uzatmaSayisi: hedef.uzatmaSayisi,
            ilgiliMahkemeKararDetayi: hedef.ilgiliMahkemeKararDetayi
        };
    }

    private handleSuccess(response: unknown): void {
        this.loading = false;
        this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Mahkeme karar talebi başarıyla gönderildi.'
        });
        this.clearForm();
    }

    private handleError(err: any): void {
        this.errorHandlingService.handleError(err, () => {
            this.loading = false;
        });
    }

    editHedef(hedef: any, index: number): void {
        // In a real implementation, this would open an edit dialog
        console.log('Edit hedef:', hedef, 'at index:', index);
    }

    clearForm(): void {
        this.commonForm.reset();
        this.idYeniKararForm.reset();
        this.idUzatmaKarariForm.reset();
        this.idSonlandirmaKarariForm.reset();
        this.idHedefGuncellemeForm.reset();
        this.idMahkemeKararGuncellemeForm.reset();
        this.idAidiyatGuncellemeForm.reset();
        this.idSucTipiGuncellemeForm.reset();
        this.itKararForm.reset();

        // Clear hedef data
        this.hedefDetayListesi.clear();

        // Reset file selection
        this.seciliDosya = null;

        // Reset aidiyat kodları options
        this.aidiyatKodlariOptions = [];
        this.newAidiyatKodu = '';

        // Reset to default karar türü
        this.kararTuru = KararTuruEnum.IletisiminDenetlenmesiYeniKarar;
        this.commonForm.get('kararTuru')?.setValue(this.kararTuru);
        this.activeTabIndex = 0;

        this.messageService.add({
            severity: 'info',
            summary: 'Temizlendi',
            detail: 'Form başarıyla temizlendi.'
        });
    }

    fillTestDataForSelectedType(): void {
        switch (this.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                this.fillIDYeniKararTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                this.fillIDUzatmaKarariTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                this.fillIDSonlandirmaKarariTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                this.fillIDHedefGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                this.fillIDMahkemeKararGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                this.fillIDAidiyatGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                this.fillIDSucTipiGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminTespiti:
                this.fillITKararTestData();
                break;
            default:
                this.fillCommonTestData();
                break;
        }
    }

    fillCommonTestData(): void {
        // Get valid values from loaded dropdown options
        const validIlKodu = this.iller.length > 0 ? this.iller[0].value : '3400'; // Default to Istanbul if available
        const validMahkemeKodu = this.mahkemeKodlari.length > 0 ? this.mahkemeKodlari[0].value : '08030100';

        this.commonForm.patchValue({
            evrakNo: '2024-TEST-001',
            evrakTarihi: new Date(),
            evrakKurumKodu: '02', // EGMIDB kurumu
            evrakTuru: EvrakTuruEnum.IletisiminDenetlenmesi,
            geldigiIlIlceKodu: validIlKodu,
            havaleBirimi: '02', //todo 02 egm, 03 mit, 04 jandarma
            evrakKonusu: 'İletişimin Denetlenmesi Test Talebi',
            evrakAciklama: 'Test amaçlı mahkeme karar talebi - Otomatik doldurulmuş',
            acilmi: true,
            mahkemeKararTipi: MahkemeKararTipiEnum.AdliHakimKarari, // Doğrudan enum değeri kullanıyoruz
            mahkemeKararDetaylari: {
                mahkemeKodu: validMahkemeKodu,
                mahkemeKararNo: '2024/TEST-123',
                mahkemeIlIlceKodu: validIlKodu,
                sorusturmaNo: '2024-SOR-001',
                aciklama: 'Test mahkeme kararı açıklaması'
            }
        });

        // Create and set test PDF file
        this.createTestFile('test-mahkeme-karar.pdf');

        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'Ortak alanlara test verisi yüklendi ve test PDF dosyası oluşturuldu.'
        });
    }

    // Helper method to add test options to dropdown arrays
    addTestOptionsToDropdown(optionsArray: any[], testValues: string[]): void {
        testValues.forEach((value) => {
            const exists = optionsArray.some((option) => option.value === value);
            if (!exists) {
                optionsArray.push({
                    label: value,
                    value: value
                });
            }
        });
    }

    fillIDYeniKararTestData(): void {
        this.fillCommonTestData();

        // Test aidiyat kodları - options'a ekle
        const testAidiyatKodlari = ['********', '********'];
        this.addTestOptionsToDropdown(this.aidiyatKodlariOptions, testAidiyatKodlari);
        // Hedef aidiyat kodları için de aynı kodları ekle
        this.addTestOptionsToDropdown(this.hedefAidiyatKodlariOptions, testAidiyatKodlari);

        // Test suç tipi kodları - mevcut options'lardan al veya geçerli kodlar ekle
        let testSucTipiKodlari: string[];
        if (this.sucTipleri.length > 0) {
            // Mevcut suç tipi kodlarından ilk ikisini al
            testSucTipiKodlari = this.sucTipleri.slice(0, 2).map((item) => item.value);
        } else {
            // Fallback olarak geçerli kodlar
            testSucTipiKodlari = ['TEST_SUC_001', 'TEST_SUC_002'];
            this.addTestOptionsToDropdown(this.sucTipleri, testSucTipiKodlari);
        }

        // Form'a test verilerini set et
        this.idYeniKararForm.patchValue({
            mahkemeAidiyatKodlari: testAidiyatKodlari,
            mahkemeSucTipiKodlari: testSucTipiKodlari
        });

        // Test hedefleri ekle - yeni array yapısı
        this.hedefler.length = 0; // Clear existing
        this.hedefler.push(
            ...[
                {
                    hedefNo: 'YNI001',
                    hedefTip: HedefTipEnum.Gsm,
                    hedefAd: 'Mehmet',
                    hedefSoyad: 'Test',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '30',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['********'],
                    canakNo: 'CANAK001'
                },
                {
                    hedefNo: 'YNI002',
                    hedefTip: HedefTipEnum.Imei,
                    hedefAd: 'Ahmet',
                    hedefSoyad: 'Örnek',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '45',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['********'],
                    canakNo: 'CANAK002'
                }
            ]
        );

        this.createTestFile('id_yeni_karar_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Yeni Karar için test verisi yüklendi ve hedefler eklendi.'
        });
    }

    fillIDUzatmaKarariTestData(): void {
        this.fillCommonTestData();

        // Test aidiyat kodları - Y ile başlayan ve 8 karakter olan geçerli kodlar
        const testAidiyatKodlari = ['********', '********', '********'];
        this.addTestOptionsToDropdown(this.aidiyatKodlariOptions, testAidiyatKodlari);
        // Hedef aidiyat kodları için de aynı kodları ekle
        this.addTestOptionsToDropdown(this.hedefAidiyatKodlariOptions, testAidiyatKodlari);

        // Test suç tipi kodları - mevcut options'lardan al veya geçerli kodlar ekle
        let testSucTipiKodlari: string[];
        if (this.sucTipleri.length > 0) {
            // Mevcut suç tipi kodlarından ilk ikisini al
            testSucTipiKodlari = this.sucTipleri.slice(0, 2).map((item) => item.value);
        } else {
            // Fallback olarak geçerli kodlar
            testSucTipiKodlari = ['TEST_SUC_UZT_001', 'TEST_SUC_UZT_002'];
            this.addTestOptionsToDropdown(this.sucTipleri, testSucTipiKodlari);
        }

        // Form'a test verilerini set et
        this.idUzatmaKarariForm.patchValue({
            mahkemeAidiyatKodlari: testAidiyatKodlari,
            mahkemeSucTipiKodlari: testSucTipiKodlari
        });

        // Test hedefleri ekle - yeni array yapısı
        this.hedefler.length = 0; // Clear existing

        // Get valid values for ilgiliMahkemeKararDetayi - ortak test verisi ile aynı değerleri kullan
        // const validIlKodu = this.iller.length > 0 ? this.iller[0].value : '3400';
        // const validMahkemeKodu = this.mahkemeKodlari.length > 0 ? this.mahkemeKodlari[0].value : '08030100';
        const mockHedefTipi1 = HedefTipEnum.Gsm;
        const mockHedefTipi2 = HedefTipEnum.Gsm;
        const mockHedefNo1 = '55511121111'; //should be in db table HEDEFLER
        const mockHedefNo2 = '55511131111'; //should be in db table HEDEFLER
        const mockIlgiliMahkemeKararDetayi: MahkemeKararDetay = {
            //should be in db table MAHKEME_KARAR
            mahkemeKodu: '06000101',
            mahkemeIlIlceKodu: '0600',
            mahkemeKararNo: 'MK-2023-001',
            sorusturmaNo: '2025/123',
            aciklama: 'Test mahkeme kararı açıklaması'
        };
        this.hedefler.push(
            ...[
                {
                    hedefNo: mockHedefNo1,
                    hedefTip: mockHedefTipi1,
                    hedefAd: 'Uzatma',
                    hedefSoyad: 'Test1',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '60',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['********', '********'],
                    canakNo: null, //Uzatma kararında hedefe CANAK numarası girilemez.
                    uzatmaSayisi: 2,
                    tcKimlikNo: '12345678901',
                    acilmi: true,
                    ilgiliMahkemeKararDetayi: mockIlgiliMahkemeKararDetayi
                },
                {
                    hedefNo: mockHedefNo2,
                    hedefTip: mockHedefTipi2,
                    hedefAd: 'Uzatma',
                    hedefSoyad: 'Test2',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '90',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['********'],
                    canakNo: null, //Uzatma kararında hedefe CANAK numarası girilemez.
                    uzatmaSayisi: 1,
                    tcKimlikNo: '98765432109',
                    acilmi: false,
                    ilgiliMahkemeKararDetayi: mockIlgiliMahkemeKararDetayi
                }
            ]
        );

        this.createTestFile('id_uzatma_karari_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Uzatma Kararı için test verisi yüklendi ve hedefler eklendi.'
        });
    }

    fillIDSonlandirmaKarariTestData(): void {
        this.fillCommonTestData();

        // Test aidiyat kodları - options'a ekle
        const testAidiyatKodlari = ['AID004', 'AID005'];
        this.addTestOptionsToDropdown(this.aidiyatKodlariOptions, testAidiyatKodlari);
        // Hedef aidiyat kodları için de aynı kodları ekle
        this.addTestOptionsToDropdown(this.hedefAidiyatKodlariOptions, testAidiyatKodlari);

        // Test suç tipi kodları - mevcut options'lardan al veya geçerli kodlar ekle
        let testSucTipiKodlari: string[];
        if (this.sucTipleri.length > 0) {
            // Mevcut suç tipi kodlarından ilk ikisini al
            testSucTipiKodlari = this.sucTipleri.slice(0, 2).map((item) => item.value);
        } else {
            // Fallback olarak geçerli kodlar
            testSucTipiKodlari = ['TEST_SUC_SON_001', 'TEST_SUC_SON_002'];
            this.addTestOptionsToDropdown(this.sucTipleri, testSucTipiKodlari);
        }

        // Form'a test verilerini set et
        this.idSonlandirmaKarariForm.patchValue({
            mahkemeAidiyatKodlari: testAidiyatKodlari,
            mahkemeSucTipiKodlari: testSucTipiKodlari
        });

        // İlgili mahkeme kararı detayı - uzatma kararındaki aynı değerler ile set et
        const mockIlgiliMahkemeKararDetayi: MahkemeKararDetay = {
            //should be in db table MAHKEME_KARAR
            mahkemeKodu: '06000101',
            mahkemeIlIlceKodu: '0600',
            mahkemeKararNo: 'MK-2023-001',
            sorusturmaNo: '2025/123',
            aciklama: 'Test mahkeme kararı açıklaması'
        };

        // Test hedefleri ekle - yeni array yapısı
        this.hedefler.length = 0; // Clear existing
        this.hedefler.push(
            ...[
                {
                    hedefNo: 'SON001',
                    hedefTip: HedefTipEnum.Gsm,
                    hedefAd: 'Sonlandırma',
                    hedefSoyad: 'Test1',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '0',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['AID004'],
                    canakNo: 'CANAK003',
                    ilgiliMahkemeKararDetayi: mockIlgiliMahkemeKararDetayi
                },
                {
                    hedefNo: 'SON002',
                    hedefTip: HedefTipEnum.Imei,
                    hedefAd: 'Sonlandırma',
                    hedefSoyad: 'Test2',
                    baslamaTarihi: new Date().toISOString(),
                    sure: '0',
                    sureTip: SureTipEnum.Gun,
                    hedefAidiyatKodlari: ['AID005'],
                    canakNo: 'CANAK004',
                    ilgiliMahkemeKararDetayi: mockIlgiliMahkemeKararDetayi
                }
            ]
        );

        this.createTestFile('id_sonlandirma_karari_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Sonlandırma Kararı için test verisi yüklendi ve hedefler eklendi.'
        });
    }

    fillIDHedefGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Test hedef güncelleme karar detayları ekle
        this.hedefGuncellemeKararDetayListesi = [
            {
                mahkemeKararDetay: {
                    mahkemeKodu: 'MHK001',
                    mahkemeKararNo: 'KARAR001',
                    sorusturmaNo: 'SOR001',
                    mahkemeIlIlceKodu: '34/01',
                    aciklama: 'Test mahkeme kararı'
                },
                hedefGuncellemeDetayListesi: [
                    {
                        hedef: {
                            hedefNo: 'HDF001',
                            hedefTip: 'GSM'
                        },
                        hedefGuncellemeBilgiListesi: [
                            {
                                hedefGuncellemeAlan: 'AD',
                                yeniDegeri: 'Yeni Ad'
                            },
                            {
                                hedefGuncellemeAlan: 'SOYAD',
                                yeniDegeri: 'Yeni Soyad'
                            }
                        ]
                    }
                ]
            }
        ];

        this.createTestFile('id_hedef_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Hedef Güncelleme için test verisi yüklendi ve karar detayları eklendi.'
        });
    }

    fillIDMahkemeKararGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Test mahkeme karar güncelleme detayları ekle
        this.mahkemeKararGuncellemeDetayListesi = [
            {
                mahkemeKararDetay: {
                    mahkemeKodu: 'MHK002',
                    mahkemeKararNo: 'KARAR002',
                    sorusturmaNo: 'SOR002',
                    mahkemeIlIlceKodu: '34/02',
                    aciklama: 'Test mahkeme karar güncelleme'
                },
                mahkemeKararGuncellemeBilgiListesi: [
                    {
                        mahkemeKararGuncellemeAlanTuru: 'MAHKEME_KODU',
                        yeniDegeri: 'MHK002_UPDATED'
                    },
                    {
                        mahkemeKararGuncellemeAlanTuru: 'ACIKLAMA',
                        yeniDegeri: 'Güncellenmiş açıklama'
                    }
                ]
            }
        ];

        this.createTestFile('id_mahkeme_karar_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Mahkeme Karar Güncelleme için test verisi yüklendi ve güncelleme detayları eklendi.'
        });
    }

    fillIDAidiyatGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Test aidiyat güncelleme karar detayları ekle
        this.aidiyatGuncellemeKararDetayListesi = [
            {
                mahkemeKararDetay: {
                    mahkemeKodu: 'MHK003',
                    mahkemeKararNo: 'KARAR003',
                    sorusturmaNo: 'SOR003',
                    mahkemeIlIlceKodu: '34/03',
                    aciklama: 'Test aidiyat güncelleme kararı'
                },
                aidiyatGuncellemeBilgiListesi: [
                    {
                        aidiyatGuncellemeAlanTuru: 'AIDIYAT_KODU',
                        yeniDegeri: 'AID_UPDATED_001'
                    },
                    {
                        aidiyatGuncellemeAlanTuru: 'AIDIYAT_ADI',
                        yeniDegeri: 'Güncellenmiş Aidiyat Adı'
                    }
                ]
            }
        ];

        this.createTestFile('id_aidiyat_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Aidiyat Güncelleme için test verisi yüklendi ve aidiyat güncelleme detayları eklendi.'
        });
    }

    fillIDSucTipiGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Test suç tipi güncelleme karar detayları ekle
        this.sucTipiGuncellemeKararDetayListesi = [
            {
                mahkemeKararDetay: {
                    mahkemeKodu: 'MHK004',
                    mahkemeKararNo: 'KARAR004',
                    sorusturmaNo: 'SOR004',
                    mahkemeAdi: 'Test Mahkemesi 4'
                },
                sucTipiGuncellemeDetay: {
                    eskiSucTipiKodlari: ['ESK_SUC_001', 'ESK_SUC_002'],
                    yeniSucTipiKodlari: ['YENI_SUC_001', 'YENI_SUC_002'],
                    guncellemeAciklamasi: 'Test suç tipi güncelleme açıklaması'
                }
            }
        ];

        this.createTestFile('id_suc_tipi_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Suç Tipi Güncelleme için test verisi yüklendi ve suç tipi güncelleme detayları eklendi.'
        });
    }

    fillITKararTestData(): void {
        this.fillCommonTestData();
        // IT Karar için evrakTuru ve mahkemeKararTipi değerlerini güncelle
        this.commonForm.patchValue({
            evrakTuru: EvrakTuruEnum.IletisiminTespiti,
            mahkemeKararTipi: MahkemeKararTipiEnum.OnleyiciHakimKarari // IT Karar için uygun mahkeme karar tipi
        });

        // Test IT hedef detayları ekle
        this.itHedefDetayListesi = [
            {
                sorguTipi: 'TELEFON_GORUSME',
                hedef: {
                    hedefNo: '5551234567',
                    hedefTip: 'GSM'
                },
                karsiHedef: {
                    hedefNo: '5559876543',
                    hedefTip: 'GSM'
                },
                baslamaTarihi: new Date().toISOString(),
                bitisTarihi: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 gün sonra
                tespitTuru: '10',
                tespitTuruDetay: '3',
                aciklama: 'Test IT hedef detayı - telefon görüşme'
            },
            {
                sorguTipi: 'IMEI_GORUSME',
                hedef: {
                    hedefNo: '123456789012345',
                    hedefTip: 'IMEI'
                },
                baslamaTarihi: new Date().toISOString(),
                bitisTarihi: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 gün sonra
                tespitTuru: '20',
                aciklama: 'Test IT hedef detayı - IMEI görüşme'
            }
        ];

        this.createTestFile('it_karar_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'IT Karar için test verisi yüklendi ve IT hedef detayları eklendi.'
        });
    }

    // Hedef dialog methods
    hedefEkleDialog(): void {
        this.editingHedefIndex = -1;
        this.hedefForm.reset();
        this.hedefForm.patchValue({
            baslamaTarihi: new Date(),
            hedefTip: HedefTipEnum.Gsm,
            sureTip: SureTipEnum.Gun
        });

        // Uzatma ve sonlandırma kararı için ilgiliMahkemeKararDetayi alanını zorunlu yap
        if (this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari ||
            this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari) {
            this.hedefForm.get('ilgiliMahkemeKararDetayi')?.setValidators([Validators.required]);
        } else {
            this.hedefForm.get('ilgiliMahkemeKararDetayi')?.clearValidators();
        }

        // Uzatma kararı için uzatmaSayisi alanını zorunlu yap
        if (this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari) {
            this.hedefForm.get('uzatmaSayisi')?.setValidators([Validators.required, Validators.min(1)]);
        } else {
            this.hedefForm.get('uzatmaSayisi')?.clearValidators();
        }
        this.hedefForm.get('ilgiliMahkemeKararDetayi')?.updateValueAndValidity();
        this.hedefForm.get('uzatmaSayisi')?.updateValueAndValidity();

        this.hedefDialogVisible = true;
    }

    hedefDuzenleDialog(index: number): void {
        this.editingHedefIndex = index;
        const hedef = this.hedefler[index];

        // Handle hedefAidiyatKodlari as array or string
        let hedefAidiyatKodlari: string[] = [];
        if (Array.isArray(hedef.hedefAidiyatKodlari)) {
            hedefAidiyatKodlari = [...hedef.hedefAidiyatKodlari];
        } else if (typeof hedef.hedefAidiyatKodlari === 'string') {
            hedefAidiyatKodlari = hedef.hedefAidiyatKodlari
                .split(',')
                .map((k: string) => k.trim())
                .filter((k: string) => k);
        }

        this.hedefForm.patchValue({
            ...hedef,
            hedefTip: hedef.hedefTip,
            baslamaTarihi: hedef.baslamaTarihi ? new Date(hedef.baslamaTarihi) : new Date(),
            hedefAidiyatKodlari: hedefAidiyatKodlari
        });

        // Uzatma ve sonlandırma kararı için ilgiliMahkemeKararDetayi alanını zorunlu yap
        if (this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari ||
            this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari) {
            this.hedefForm.get('ilgiliMahkemeKararDetayi')?.setValidators([Validators.required]);
        } else {
            this.hedefForm.get('ilgiliMahkemeKararDetayi')?.clearValidators();
        }

        // Uzatma kararı için uzatmaSayisi alanını zorunlu yap
        if (this.kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari) {
            this.hedefForm.get('uzatmaSayisi')?.setValidators([Validators.required, Validators.min(1)]);
        } else {
            this.hedefForm.get('uzatmaSayisi')?.clearValidators();
        }
        this.hedefForm.get('ilgiliMahkemeKararDetayi')?.updateValueAndValidity();
        this.hedefForm.get('uzatmaSayisi')?.updateValueAndValidity();

        this.hedefDialogVisible = true;
    }

    hedefKaydet(): void {
        if (this.hedefForm.valid) {
            const hedefData = this.hedefForm.value;

            // Ensure hedefAidiyatKodlari is an array
            const hedefAidiyatKodlari = Array.isArray(hedefData.hedefAidiyatKodlari) ? hedefData.hedefAidiyatKodlari : [];

            const hedef = {
                ...hedefData,
                baslamaTarihi: hedefData.baslamaTarihi ? hedefData.baslamaTarihi.toISOString() : new Date().toISOString(),
                hedefAidiyatKodlari: hedefAidiyatKodlari
            };

            if (this.editingHedefIndex >= 0) {
                // Update existing hedef
                this.hedefler[this.editingHedefIndex] = hedef;
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef güncellendi',
                    life: 3000
                });
            } else {
                // Add new hedef
                this.hedefler.push(hedef);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef eklendi',
                    life: 3000
                });
            }

            this.hedefDialogVisible = false;
            this.hedefForm.reset();
        } else {
            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: 'Lütfen tüm zorunlu alanları doldurun'
            });
        }
    }

    hedefSil(index: number): void {
        this.hedefler.splice(index, 1);
        this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Hedef silindi',
            life: 3000
        });
    }

    hedefDialogKapat(): void {
        this.hedefDialogVisible = false;
        this.hedefForm.reset();
        this.editingHedefIndex = -1;
    }

    getSureTipiLabel(value: SureTipEnum): string {
        const option = this.sureTipiOptions.find((opt) => opt.value === value);
        return option ? option.label : value;
    }

    formatTarih(tarih: Date | string): string {
        if (!tarih) return '';
        const date = new Date(tarih);
        return date.toLocaleDateString('tr-TR');
    }

    getHedefTipLabel(value: HedefTipEnum): string {
        const option = this.hedefTipOptions.find((opt) => opt.value === value);
        return option ? option.label : value;
    }

    // Form validation helpers for hedef form
    isHedefFieldInvalid(fieldName: string): boolean {
        const field = this.hedefForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getHedefFieldError(fieldName: string): string {
        const field = this.hedefForm.get(fieldName);
        if (field && field.errors) {
            if (field.errors['required']) {
                if (fieldName === 'ilgiliMahkemeKararDetayi') {
                    return 'İlgili Mahkeme Karar Detayı zorunludur';
                }
                if (fieldName === 'uzatmaSayisi') {
                    return 'Uzatma Sayısı zorunludur';
                }
                return 'Bu alan zorunludur';
            }
            if (field.errors['min']) {
                if (fieldName === 'uzatmaSayisi') {
                    return 'Uzatma Sayısı en az 1 olmalıdır';
                }
                return 'Geçersiz değer';
            }
            if (field.errors['invalidMahkemeKararDetay']) {
                return 'Mahkeme karar detayları eksik veya hatalı';
            }
        }
        return '';
    }

    // getHedefAidiyatKodlariArray(aidiyatKodlari: any): string[] {
    //   if (!aidiyatKodlari) {
    //     return [];
    //   }

    //   if (Array.isArray(aidiyatKodlari)) {
    //     return aidiyatKodlari.filter((kod: string) => kod && kod.trim().length > 0);
    //   }

    //   if (typeof aidiyatKodlari === 'string') {
    //     return aidiyatKodlari
    //       .split(',')
    //       .map((kod: string) => kod.trim())
    //       .filter((kod: string) => kod.length > 0 && kod !== 'null');
    //   }

    //   return [];
    // }

    getHedefAidiyatKodlariArray(kodlar: string | string[]): string[] {
        if (!kodlar) return [];
        if (Array.isArray(kodlar)) return kodlar;
        return kodlar
            .split(',')
            .map((k) => k.trim())
            .filter((k) => k);
    }

    getHedefAidiyatKodlariDisplay(kodlar: string | string[]): string {
        const array = this.getHedefAidiyatKodlariArray(kodlar);
        if (array.length === 0) return '';
        if (array.length === 1) return array[0];
        if (array.length === 2) return array.join(', ');
        return `${array[0]}, ${array[1]}... (+${array.length - 2} daha)`;
    }

    onFileSelect(event: any): void {
        this.seciliDosya = event.files && event.files.length > 0 ? event.files[0] : null;
    }

    onFileRemove(event?: any): void {
        this.seciliDosya = null;
    }

    // Utility methods
    isFieldInvalid(fieldName: string, formGroup: FormGroup = this.commonForm): boolean {
        if (!formGroup) return false;
        const field = formGroup.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getFieldError(fieldName: string, formGroup: FormGroup = this.commonForm): string {
        if (!formGroup) return '';
        const field = formGroup.get(fieldName);
        if (field && field.errors) {
            if (field.errors['required']) return 'Bu alan zorunludur';
            if (field.errors['maxlength']) return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
        }
        return '';
    }

    // Hedef güncelleme karar detayı metotları
    hedefGuncellemeKararDetayEkleDialog(): void {
        // Basit bir dialog açma simülasyonu - gerçek implementasyon için dialog component'i gerekli
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Hedef güncelleme karar detayı ekleme özelliği geliştirilecek.'
        });
    }

    hedefGuncellemeKararDetayDuzenleDialog(index: number): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: `${index + 1}. karar detayı düzenleme özelliği geliştirilecek.`
        });
    }

    hedefGuncellemeKararDetaySil(index: number): void {
        if (confirm('Bu karar detayını silmek istediğinizden emin misiniz?')) {
            this.hedefGuncellemeKararDetayListesi.splice(index, 1);
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'Karar detayı silindi.'
            });
        }
    }

    getHedefGuncellemeAlanLabel(alan: string): string {
        const labels: { [key: string]: string } = {
            AD: 'Ad',
            SOYAD: 'Soyad',
            TCKIMlIKNO: 'TC Kimlik No',
            CANAK_NO: 'Çanak No'
        };
        return labels[alan] || alan;
    }

    // Mahkeme karar güncelleme detayı metotları
    mahkemeKararGuncellemeDetayEkleDialog(): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Mahkeme karar güncelleme detayı ekleme özelliği geliştirilecek.'
        });
    }

    mahkemeKararGuncellemeDetayDuzenleDialog(index: number): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: `${index + 1}. güncelleme detayı düzenleme özelliği geliştirilecek.`
        });
    }

    mahkemeKararGuncellemeDetaySil(index: number): void {
        if (confirm('Bu güncelleme detayını silmek istediğinizden emin misiniz?')) {
            this.mahkemeKararGuncellemeDetayListesi.splice(index, 1);
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'Güncelleme detayı silindi.'
            });
        }
    }

    getMahkemeKararGuncellemeAlanLabel(alanTuru: string): string {
        const labels: { [key: string]: string } = {
            MAHKEME_KODU: 'Mahkeme Kodu',
            MAHKEME_KARAR_NO: 'Mahkeme Karar No',
            SORUSTURMA_NO: 'Soruşturma No',
            MAHKEME_IL_ILCE_KODU: 'Mahkeme İl/İlçe Kodu',
            ACIKLAMA: 'Açıklama'
        };
        return labels[alanTuru] || alanTuru;
    }

    // Aidiyat güncelleme karar detayı metotları
    aidiyatGuncellemeKararDetayEkleDialog(): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Aidiyat güncelleme karar detayı ekleme özelliği geliştirilecek.'
        });
    }

    aidiyatGuncellemeKararDetayDuzenleDialog(index: number): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: `${index + 1}. aidiyat güncelleme detayı düzenleme özelliği geliştirilecek.`
        });
    }

    aidiyatGuncellemeKararDetaySil(index: number): void {
        if (confirm('Bu aidiyat güncelleme detayını silmek istediğinizden emin misiniz?')) {
            this.aidiyatGuncellemeKararDetayListesi.splice(index, 1);
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'Aidiyat güncelleme detayı silindi.'
            });
        }
    }

    getAidiyatGuncellemeAlanLabel(alanTuru: string): string {
        const labels: { [key: string]: string } = {
            AIDIYAT_KODU: 'Aidiyat Kodu',
            AIDIYAT_ADI: 'Aidiyat Adı',
            AIDIYAT_TIPI: 'Aidiyat Tipi',
            ACIKLAMA: 'Açıklama'
        };
        return labels[alanTuru] || alanTuru;
    }

    // Suç tipi güncelleme karar detayı metotları
    sucTipiGuncellemeKararDetayEkleDialog(): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Suç tipi güncelleme detayı ekleme özelliği geliştirilecek.'
        });
    }

    sucTipiGuncellemeKararDetayDuzenleDialog(index: number): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: `${index + 1}. suç tipi güncelleme detayı düzenleme özelliği geliştirilecek.`
        });
    }

    sucTipiGuncellemeKararDetaySil(index: number): void {
        if (confirm('Bu suç tipi güncelleme detayını silmek istediğinizden emin misiniz?')) {
            this.sucTipiGuncellemeKararDetayListesi.splice(index, 1);
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'Suç tipi güncelleme detayı silindi.'
            });
        }
    }

    // IT hedef detayı metotları
    itHedefDetayEkleDialog(): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'IT hedef detayı ekleme özelliği geliştirilecek.'
        });
    }

    itHedefDetayDuzenleDialog(index: number): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: `${index + 1}. IT hedef detayı düzenleme özelliği geliştirilecek.`
        });
    }

    itHedefDetaySil(index: number): void {
        if (confirm('Bu IT hedef detayını silmek istediğinizden emin misiniz?')) {
            this.itHedefDetayListesi.splice(index, 1);
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'IT hedef detayı silindi.'
            });
        }
    }

    getSorguTipiLabel(sorguTipi: string): string {
        const labels: { [key: string]: string } = {
            TELEFON_GORUSME: 'Telefon Görüşme',
            IMEI_GORUSME: 'IMEI Görüşme',
            IMEI_KULLANAN_NUMARA: 'IMEI Kullanan Numara'
        };
        return labels[sorguTipi] || sorguTipi;
    }

    // Preview metotları
    showPreview(): void {
        this.previewVisible = true;
    }

    getPreviewData(): any {
        const commonData = this.commonForm.value;
        const specificData = this.getSpecificFormDataForPreview();

        return {
            ...commonData,
            ...specificData
        };
    }

    private getSpecificFormDataForPreview(): any {
        switch (this.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                return {
                    hedefDetayListesi: this.hedefler || [],
                    mahkemeAidiyatKodlari: this.getFormValue('mahkemeAidiyatKodlari'),
                    mahkemeSucTipiKodlari: this.getFormValue('mahkemeSucTipiKodlari')
                };
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                return {
                    hedefGuncellemeKararDetayListesi: this.hedefGuncellemeKararDetayListesi || []
                };
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                return {
                    mahkemeKararGuncellemeDetayListesi: this.mahkemeKararGuncellemeDetayListesi || []
                };
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                return {
                    aidiyatGuncellemeKararDetayListesi: this.aidiyatGuncellemeKararDetayListesi || []
                };
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                return {
                    sucTipiGuncellemeKararDetayListesi: this.sucTipiGuncellemeKararDetayListesi || []
                };
            case KararTuruEnum.IletisiminTespiti:
                return {
                    itHedefDetayListesi: this.itHedefDetayListesi || []
                };
            default:
                return {};
        }
    }

    private getFormValue(controlName: string): any {
        const currentForm = this.getCurrentForm();
        return currentForm?.get(controlName)?.value || [];
    }

    private getCurrentForm(): any {
        switch (this.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                return this.idYeniKararForm;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                return this.idUzatmaKarariForm;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                return this.idSonlandirmaKarariForm;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                return this.idHedefGuncellemeForm;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                return this.idMahkemeKararGuncellemeForm;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                return this.idAidiyatGuncellemeForm;
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                return this.idSucTipiGuncellemeForm;
            case KararTuruEnum.IletisiminTespiti:
                return this.itKararForm;
            default:
                return null;
        }
    }

    // JSON Import/Export metotları
    showJsonImportExport(): void {
        this.jsonImportExportVisible = true;
    }

    importFromJson(jsonData: any): void {
        try {
            // Common form verilerini doldur
            if (jsonData.commonForm) {
                this.commonForm.patchValue(jsonData.commonForm);
            }

            // Specific form verilerini doldur
            this.importSpecificFormData(jsonData);

            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: 'JSON verisi başarıyla içe aktarıldı ve form alanları dolduruldu.'
            });
        } catch (error) {
            this.messageService.add({
                severity: 'error',
                summary: 'Hata',
                detail: 'JSON verisi içe aktarılırken hata oluştu.'
            });
        }
    }

    private importSpecificFormData(jsonData: any): void {
        switch (this.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                this.importIDFormData(jsonData);
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                if (jsonData.hedefGuncellemeKararDetayListesi) {
                    this.hedefGuncellemeKararDetayListesi = jsonData.hedefGuncellemeKararDetayListesi;
                }
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                if (jsonData.mahkemeKararGuncellemeDetayListesi) {
                    this.mahkemeKararGuncellemeDetayListesi = jsonData.mahkemeKararGuncellemeDetayListesi;
                }
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                if (jsonData.aidiyatGuncellemeKararDetayListesi) {
                    this.aidiyatGuncellemeKararDetayListesi = jsonData.aidiyatGuncellemeKararDetayListesi;
                }
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme:
                if (jsonData.sucTipiGuncellemeKararDetayListesi) {
                    this.sucTipiGuncellemeKararDetayListesi = jsonData.sucTipiGuncellemeKararDetayListesi;
                }
                break;
            case KararTuruEnum.IletisiminTespiti:
                if (jsonData.itHedefDetayListesi) {
                    this.itHedefDetayListesi = jsonData.itHedefDetayListesi;
                }
                break;
        }
    }

    private importIDFormData(jsonData: any): void {
        const currentForm = this.getCurrentForm();
        if (currentForm) {
            // Aidiyat ve suç tipi kodlarını doldur
            if (jsonData.mahkemeAidiyatKodlari) {
                currentForm.patchValue({ mahkemeAidiyatKodlari: jsonData.mahkemeAidiyatKodlari });
            }
            if (jsonData.mahkemeSucTipiKodlari) {
                currentForm.patchValue({ mahkemeSucTipiKodlari: jsonData.mahkemeSucTipiKodlari });
            }

            // Hedef detaylarını doldur
            if (jsonData.hedefDetayListesi) {
                this.hedefDetayListesi.clear();
                jsonData.hedefDetayListesi.forEach((hedef: any) => {
                    this.hedefDetayListesi.push(this.fb.group(hedef));
                });
            }
        }
    }

    addTestHedef(adi: string = 'Mehmet', soyadi: string = 'Örnek', hedefNo: string = '1'): void {
        // Ensure test aidiyat codes are available in both options arrays
        const testAidiyatCodes = ['AIDIYAT1', 'AIDIYAT2'];
        testAidiyatCodes.forEach((code) => {
            if (!this.hedefAidiyatKodlariOptions.includes(code)) {
                this.hedefAidiyatKodlariOptions.push(code);
            }
        });

        const testHedef = this.fb.group({
            hedefNo: [hedefNo, Validators.required],
            hedefTip: [HedefTipEnum.Gsm, Validators.required],
            hedefAd: [adi, Validators.required],
            hedefSoyad: [soyadi, Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: ['30'],
            sureTip: [SureTipEnum.Gun, Validators.required],
            hedefAidiyatKodlari: [testAidiyatCodes, Validators.required],
            canakNo: ['CN-TEST-001']
        });

        this.hedefDetayListesi.push(testHedef);
    }

    private createTestFile(fileName: string): void {
        // Create a simple test PDF file
        const pdfContent =
            '%PDF-1.4\n%âãÏÓ\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Test PDF Document) Tj\nET\nendstream\nendobj\ntrailer\n<<\n/Root 1 0 R\n>>\n%%EOF';

        const blob = new Blob([pdfContent], { type: 'application/pdf' });
        this.seciliDosya = new File([blob], fileName, { type: 'application/pdf' });
    }
}
